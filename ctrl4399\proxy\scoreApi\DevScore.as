package ctrl4399.proxy.scoreApi
{
   public interface DevScore
   {
      
      function getTop(param1:int, param2:String, param3:int, param4:int, param5:Function, param6:Function) : void;
      
      function submitScore(param1:int, param2:String, param3:int, param4:String, param5:String, param6:int, param7:int, param8:Function, param9:Function) : void;
      
      function test(param1:String, param2:Function, param3:Function) : void;
   }
}

