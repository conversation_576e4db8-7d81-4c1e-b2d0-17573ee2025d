package ctrl4399.proxy.union<PERSON><PERSON>
{
   public interface Master<PERSON><PERSON>
   {
      
      function applyList(param1:<PERSON><PERSON><PERSON>ead<PERSON>, param2:int, param3:int, param4:Function, param5:Function) : void;
      
      function applyAudit(param1:<PERSON><PERSON><PERSON>eader, param2:int, param3:String, param4:int, param5:Function, param6:Function) : void;
      
      function memberR<PERSON>ove(param1:<PERSON><PERSON><PERSON>eader, param2:int, param3:String, param4:Function, param5:Function) : void;
      
      function dissolve(param1:ApiHeader, param2:int, param3:Function, param4:Function) : void;
      
      function deleteContributionUnion(param1:<PERSON><PERSON><PERSON>eader, param2:int, param3:Function, param4:Function) : void;
      
      function applyAuditMuch(param1:ApiHeader, param2:Array, param3:int, param4:Function, param5:Function) : void;
      
      function transfer(param1:ApiHeader, param2:int, param3:String, param4:int, param5:Function, param6:Function) : void;
      
      function test(param1:Function, param2:Function) : void;
   }
}

