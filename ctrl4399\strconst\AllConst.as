package ctrl4399.strconst
{
   public class AllConst
   {
      
      public static var MODE_OBJECT:Object = null;
      
      public static var AD_INFO:Object = {
         "contentId":"0000000034",
         "publicsherId":"ca-games-pub-9606551472994074",
         "adType":"text_overlay",
         "descriptionUrl":["123","456"]
      };
      
      public static var SATAry:Array = new Array();
      
      public static const MODE_NAME_SCORE:String = "integralMode";
      
      public static const MODE_NAME_SAVE:String = "saveMode";
      
      public static const MODE_NAME_GAMELIST:String = "gameListMode";
      
      public static const MODE_NAME_PAY:String = "payMoney";
      
      public static const MODE_NAME_SHOP:String = "shopMode";
      
      public static const MODE_NAME_MEDAL:String = "medalMode";
      
      public static const MODE_NAME_RANKLIST:String = "rankListMode";
      
      public static const MODE_NAME_UNION:String = "unionMode";
      
      public static const MODE_NAME_SECONDARY:String = "secondaryMode";
      
      public static const VIEW_NAME_SCORE:String = "view_name_score";
      
      public static const VIEW_NAME_LOG:String = "view_name_log";
      
      public static const VIEW_NAME_REG:String = "view_name_reg";
      
      public static const VIEW_NAME_SORT:String = "view_name_sort";
      
      public static const VIEW_NAME_GAMELIST:String = "view_name_gameList";
      
      public static const VIEW_NAME_SHOP:String = "view_name_shop";
      
      public static const VIEW_NAME_SHOP_ITEM:String = "view_name_shop_item";
      
      public static const VIEW_NAME_LOGSUCC:String = "view_name_logSucc";
      
      public static const VIEW_NAME_SECONDARY:String = "view_name_secondary";
      
      public static const PROXY_NAME_MAIN:String = "proxy_name_main";
      
      public static const PROXY_NAME_SCORE:String = "proxy_name_score";
      
      public static const PROXY_NAME_SORT:String = "proxy_name_sort";
      
      public static const PROXY_NAME_SAVE:String = "proxy_name_save";
      
      public static const PROXY_NAME_GAMELIST:String = "proxy_name_gameList";
      
      public static const PROXY_NAME_SHOP:String = "proxy_name_shop";
      
      public static const PROXY_NAME_SHOPND:String = "proxy_name_shopnd";
      
      public static const PROXY_NAME_PACKAGE:String = "proxy_name_package";
      
      public static const PROXY_NAME_FREEPACKAGE:String = "proxy_name_freepackage";
      
      public static const PROXY_NAME_MEDAL:String = "proxy_name_medal";
      
      public static const PROXY_NAME_RANKLIST:String = "proxy_name_rankList";
      
      public static const PROXY_NAME_REDEEMTASK:String = "proxy_name_redeemTask";
      
      public static const PROXY_NAME_UNION_VISITOR:String = "proxy_name_union_visitorProxy";
      
      public static const PROXY_NAME_UNION_MEMBER:String = "proxy_name_union_memberProxy";
      
      public static const PROXY_NAME_UNION_GROW:String = "proxy_name_union_growProxy";
      
      public static const PROXY_NAME_UNION_MASTER:String = "proxy_name_union_masterProxy";
      
      public static const PROXY_NAME_UNION_VARIABLE:String = "proxy_name_union_variableProxy";
      
      public static const PROXY_NAME_UNION_ROLE:String = "proxy_name_union_roleProxy";
      
      public static const PROXY_NAME_SECONDARY:String = "proxy_name_secondary";
      
      public static const MVC_SHOW_LOGBOX:String = "MVC_SHOW_LOGBOX";
      
      public static const MVC_SHOW_REGBOX:String = "MVC_SHOW_REGBOX";
      
      public static const MVC_SHOW_SCORE:String = "MVC_SHOW_SCORE";
      
      public static const MVC_SCORE_RETURN:String = "MVC_SCORE_RETURN";
      
      public static const MVC_SCORE_TIP:String = "MVC_SCORE_TIP";
      
      public static const MVC_LOG_SUCCESS:String = "MVC_LOG_SUCCESS";
      
      public static const MVC_LOG_TIP:String = "MVC_LOG_TIP";
      
      public static const MVC_LOGOUT:String = "MVC_LOGOUT";
      
      public static const MVC_SORT_RETURN:String = "MVC_SORT_RETURN";
      
      public static const MVC_SORT_REQUEST:String = "MVC_SORT_REQUEST";
      
      public static const MVC_CLOSE_PANEL:String = "MVC_CLOSE_PANEL";
      
      public static const MVC_COLSE_NOLOGWIN:String = "MVC_COLSE_NOLOGWIN";
      
      public static const MVC_SHOW_LOGOUTTIP:String = "MVC_SHOW_LOGOUTTIP";
      
      public static const MVC_GET_SESSION:String = "MVC_GET_SESSION";
      
      public static const GET_SHOP_DATA:String = "GET_SHOP_DATA";
      
      public static const MVC_SHOP_MONEY:String = "MVC_SHOP_MONEY";
      
      public static const MVC_SHOP_ERROR:String = "MVC_SHOP_ERROR";
      
      public static const MVC_SHOP_INFO:String = "MVC_SHOP_INFO";
      
      public static const MVC_SHOP_TYPE:String = "MVC_SHOP_TYPE";
      
      public static const MVC_SHOP_SHOWITEM:String = "MVC_SHOP_SHOWITEM";
      
      public static const MVC_SHOP_DECMONEY:String = "MVC_SHOP_DECMONEY";
      
      public static const MVC_SHOW_SECONDARY_LOGBOX:String = "MVC_SHOW_SECONDARY_LOGBOX";
      
      public static const MVC_HIDE_SECONDARY_LOGBOX:String = "MVC_HIDE_SECONDARY_LOGBOX";
      
      public static const MVC_SECONDARY_COM_ERROR:String = "MVC_SECONDARY_COM_ERROR";
      
      public static const MVC_SECONDARY_LOG_ERROR:String = "MVC_SECONDARY_LOG_ERROR";
      
      public static const MVC_SECONDARY_IP_ERROR:String = "MVC_SECONDARY_IP_ERROR";
      
      public static const MVC_SECONDARY_USERLOCKED_ERROR:String = "MVC_SECONDARY_USERLOCKED_ERROR";
      
      public static const MVC_SECONDARY_CHECK_CODE_ERROR:String = "MVC_SECONDARY_CHECK_CODE_ERROR";
      
      public static const SKIN_LOAD_OK:String = "skin_load_ok";
      
      public static const MVC_SHOW_GAMELIST:String = "MVC_SHOW_GAMELIST";
      
      public static const GETDATA_OK:String = "getGameListDataOk";
      
      public static const GET_AD_OK:String = "getAdOk";
      
      public static const INIT_UI:String = "INIT_UI";
      
      public static const URL_CHECK_USERINFO:String = "https://save.api.4399.com/auth/openapi.php?method=User.Authenticate";
      
      public static const URL_SCORE:String = "https://save.api.4399.com/score/DevScore.php";
      
      public static const URL_TOKEN:String = "https://save.api.4399.com/score/get_token.php";
      
      public static const URL_MEDAL:String = "http://club.4399.com/dev/submitscore_forusercenter.php";
      
      public static const URL_REDEEM_TASK:String = "https://my.4399.com/services/game-play";
      
      public static const URL_LINK_HEAD:String = "https://save.api.4399.com";
      
      public static const URL_GAME_LIST:* = "https://save.api.4399.com/recommend/api.php?ac=recommend";
      
      public static const URL_GAME_LIST_AD_REDICT:* = "https://save.api.4399.com/recommend/api.php?ac=go";
      
      public static const URL_GAME_LIST_DEFAULT_AD:* = "https://cdn.comment.4399pk.com/control/dev_recommend_logo.gif";
      
      public static const URL_GAME_LIST_DEFAULT_AD_REDICT:* = "https://www.4399.com";
      
      public static const URL_SAVE_TOKEN:String = "https://save.api.4399.com/index.php?ac=get_token";
      
      public static const URL_SAVE_SET:String = "https://save.api.4399.com/?ac=save";
      
      public static const URL_SAVE_GET:String = "https://save.api.4399.com/?ac=get";
      
      public static const URL_SAVE_LIST:String = "https://save.api.4399.com/?ac=get_list";
      
      public static const URL_GET_SERVERTIME:String = "https://save.api.4399.com/?ac=get_time";
      
      public static const URL_GET_PAY_TOKEN:String = "https://save.api.4399.com/exchange/v2/flash/GetToken";
      
      public static const URL_DEC_MONEY:String = "https://save.api.4399.com/exchange/v2/flash/Dec";
      
      public static const URL_GET_MONEY:String = "https://save.api.4399.com/exchange/v2/flash/GetMoney";
      
      public static const URL_PAY_MONEY:String = "https://save.api.4399.com/exchange/v2/flash/Pay?";
      
      public static const URL_GET_TOTAL_PAIED:String = "https://save.api.4399.com/exchange/v2/flash/GetTotalPay";
      
      public static const URL_GET_TOTAL_RECHARGED:String = "https://save.api.4399.com/exchange/v2/flash/GetTotalRecharge";
      
      private static const mallHeadUrl:String = "https://save.api.4399.com/mall/index.php/Api/";
      
      public static const URL_GET_SHOP_TOKEN:String = mallHeadUrl + "GetToken";
      
      public static const URL_GET_SHOP_INFO:String = mallHeadUrl + "GetMall";
      
      public static const URL_GET_SHOP_TYPE:String = mallHeadUrl + "GetConfig";
      
      public static const URL_GET_SHOP_ADD:String = mallHeadUrl + "AddTool";
      
      public static const URL_GET_SHOP_PACKAGE:String = mallHeadUrl + "GetTools";
      
      public static const URL_GET_SHOP_DEL:String = mallHeadUrl + "DeleteTool";
      
      public static const URL_SET_SHOP_PRO:String = mallHeadUrl + "SetProperty";
      
      public static const URL_MODIFY_SHOP_EX:String = mallHeadUrl + "SetExtend";
      
      public static const URL_CLEAR_SHOP_EXTYPE:String = mallHeadUrl + "EmptyUser";
      
      public static const URL_SHOP:String = "https://save.api.4399.com/mall/FlashStoreApi";
      
      public static const URL_RANK_LIST:String = "https://save.api.4399.com/rank/FlashScoreApi";
      
      public static const ULR_RNAK_LIST_GET_TOKEN:String = "https://save.api.4399.com/ranging.php/?ac=get_token";
      
      public static const ULR_RNAK_LIST_GET:String = "https://save.api.4399.com/ranging.php/?ac=get";
      
      public static const URL_UNION_MEMBER:String = "https://save.api.4399.com/union/MemberApi";
      
      public static const URL_UNION_VARIABLE:String = "https://save.api.4399.com/union/VariableApi";
      
      public static const URL_UNION_VISITOR:String = "https://save.api.4399.com/union/VisitorApi";
      
      public static const URL_UNION_MASTER:String = "https://save.api.4399.com/union/MasterApi";
      
      public static const URL_UNION_GROW:String = "https://save.api.4399.com/union/GrowApi";
      
      public static const URL_UNION_ROLE:String = "https://save.api.4399.com/union/RoleApi";
      
      public static const URL_SECONDARY_CHECK_LOGIN:String = "https://save.api.4399.com/secondary.php?ac=login";
      
      public static const URL_SECONDARY_SAVE_SET:String = "https://save.api.4399.com/secondary.php?ac=save";
      
      public static const URL_SECONDARY_SAVE_GET:String = "https://save.api.4399.com/secondary.php?ac=get";
      
      public static const URL_SECONDARY_SAVE_GET_LIST:String = "https://save.api.4399.com/secondary.php?ac=get_list";
      
      public static const URL_SECONDARY_CHECK_CODE:String = "https://save.api.4399.com/secondary.php?ac=get_captcha";
      
      public static const URL_SECONDARY_HAVE_CHECK_CODE:String = "https://save.api.4399.com/secondary.php?ac=check_privilege";
      
      public static const URL_GET_SECONDARY_PWD:String = "https://my.4399.com/do.php?ac=lostpasswd&source=100";
      
      public static const SPC_ScoreView:String = "SPC_ScoreView";
      
      public static const SPC_ScoreCont:String = "SPC_ScoreCont";
      
      public static const SPC_BTN_TOP:String = "SPC_BTN_TOP";
      
      public static const SPC_INPUT:String = "SPC_INPUT";
      
      public static const SPC_BTN_PRE_LOG:String = "SPC_BTN_PRE_LOG";
      
      public static const SPC_BTN_RESTART:String = "SPC_BTN_RESTART";
      
      public static const SPC_LogScoreView:String = "SPC_LogScoreView";
      
      public static const SPC_LogScoreCont:String = "SPC_LogScoreCont";
      
      public static const SPC_BTN_OVER:String = "SPC_BTN_OVER";
      
      public static const SPC_TxtPanel:String = "SPC_TxtPanel";
      
      public static const SPC_SortCont:String = "SPC_SortCont";
      
      public static const SPC_SortView:String = "SPC_SortView";
      
      public static const SPC_BTN_WORLD:String = "SPC_BTN_WORLD";
      
      public static const SPC_BTN_FRIEND:String = "SPC_BTN_FRIEND";
      
      public static const SPC_RegCont:String = "SPC_RegCont";
      
      public static const SPC_SListSelf:String = "SPC_SListSelf";
      
      public static const SPC_SListOther:String = "SPC_SListOther";
      
      public static const SPC_TabTxt:String = "_textFieldHolderClass";
      
      public static const SPC_SBtnBg:String = "SPC_SBtnBg";
      
      public static const SPC_SCROLL_DOWN:String = "_19_down";
      
      public static const SPC_SCROLL_UP:String = "_19_up";
      
      public static const SPC_SCROLL_TRACE:String = "_19_trace";
      
      public static const SPC_SCROLL_THUMB:String = "_19_thumb";
      
      public static const SPC_VIEW:String = "SPC_View";
      
      public static const SPC_CONT:String = "SPC_Cont";
      
      public static const GL_AD_MASK:String = "AdMask";
      
      public static const GL_TITLE_TOOL:String = "GL_TitleTool";
      
      public static const SPC_View_Shop:String = "SPC_View_Shop";
      
      public static const SPC_Cont_Shop:String = "SPC_Cont_Shop";
      
      public static const SPC_View_Shop_Item:String = "SPC_View_Shop_Item";
      
      public static const SPC_Cont_Shop_Item:String = "SPC_Cont_Shop_Item";
      
      public static const SPC_SecondaryLogView:String = "SPC_SecondaryLogView";
      
      public static const SET_AS2_FOCUSMANAGER:String = "setAs2FocusManager";
      
      public static const LOG_IN_NOW:String = "\'logInNow";
      
      public static const SAVE_MODE:int = 1;
      
      public static const GET_MODE:int = 2;
      
      public static const SAVE_GET_MODE:int = 3;
      
      public static const SAVE_COVER_MODE:int = 4;
      
      public static const SAVE_SUC_MODE:int = 5;
      
      public static const SAVE_LOCAL_SUC_MODE:int = 6;
      
      public static const SAVE_DATA:int = 0;
      
      public static const GET_DATA:int = 1;
      
      public static const CLOSE:int = 2;
      
      public static const SAVE_PRO:int = 3;
      
      public static const GET_PRO:int = 4;
      
      public static const SAVE_SUC_DATA:int = 5;
      
      public static const EMPTY_TIP:String = "emptyTip";
      
      public static const WAIT_MC:String = "waitMc";
      
      public static const SAVE_LIST_ITEM_CLICK:String = "saveListItemClick";
      
      public static const SAVE_TIP_DOWN:String = "saveTipDown";
      
      public static const SAVE_TIP:String = "saveTip";
      
      public static const CLOSE_BTN_CLICK:String = "closeBtnClick";
      
      public static const BTN_DOWN:String = "btnDown";
      
      public static const SPC_NET_FAILURE_BTN:String = "saveFailureViewBtn";
      
      public static const SPC_NET_FAILURE_VIEW:String = "SPC_NET_FAILURE_VIEW";
      
      public static const OPEN_NET_FAILURE_UI:String = "openNetFailureUI";
      
      public static const OPEN_SAVE_LIST_UI:String = "openSaveListUI";
      
      public static const CLOASE_SAVE_LIST_UI:String = "closeSaveListUI";
      
      public static const SAVE_LIST_ITME:String = "saveListItem";
      
      public static const SAVE_LIST_VIEW:String = "SPC_SAVE_LIST_VIEW";
      
      public static const GET_SERVER_DATA:String = "getServerData";
      
      public static const SAVE_SERVER_DATA:String = "saveServerData";
      
      public static const SAVE_DATA_RETURN:String = "saveDataReturn";
      
      public static const SAVE_ERROR:String = "saveError";
      
      public static const CLOSE_LOG_WIN:String = "closeLogWin";
      
      public static const ON_LINE_TRUE:String = "onLineTrue";
      
      public static const ON_LINE_FALSE:String = "onLineFalse";
      
      public static const DATA_FROM_LOCAL:int = 1;
      
      public static const DATA_FROM_NET:int = 2;
      
      public static const SAVE_RETURN:String = "saveReturn";
      
      public static const SAVE_EVENT_SAVE:int = 0;
      
      public static const SAVE_EVENT_GET:int = 1;
      
      public static const SAVE_EVENT_LIST:int = 2;
      
      public static const MULTIPLE_ERROR:int = 3;
      
      public static const SHOW_NET_MODE:int = 0;
      
      public static const SHOW_LOCAL_SAVE_MODE:int = 1;
      
      public static const SHOW_LOCAL_GET_MODE:int = 2;
      
      public static const USER_CLOSE_LOG_UI:String = "userCloseLogUI";
      
      public static const OPEN_SAVE_LOCAL_UI:String = "openSaveLocalUI";
      
      public static const SAVE_UI_SHOW_LOG:String = "saveUIshowLog";
      
      public static const CHANGE_SORT_TYPE:String = "changeSortType";
      
      public static const SORT_WIN_BAKC:String = "sortWinBack";
      
      public static const OPEN_SCORE_LOG_WIN:String = "openScoreLogWin";
      
      public static const SORT_BACK_BTN_SKIN:String = "sort_back_btn";
      
      public static const SORT_LOAD_PIC_DATY:int = 1;
      
      public static const SORT_LOAD_PIC_ALL:int = 2;
      
      public static const SORT_LOAD_PIC_MONTH:int = 3;
      
      public static const UPDATA_SORT_PIC:String = "updataSrotPic";
      
      public static const LOAD_SORT_DATA_ERROR:String = "loadSortDataError";
      
      public static const ERROR_TIP_UI:String = "emptyTip";
      
      public static const LOAD_GAME_LIST_ERROR:String = "loadGameListError";
      
      public static const UP_DATA_SCORE:String = "upDataScore";
      
      public static const COLSE_PANEL_4399:String = "close_panel_4399";
      
      public static const CLOSE_REG_WIN:int = 0;
      
      public static const CLOSE_LOGIN_WIN:int = 1;
      
      public static const CLOSE_GAME_LIST_WIN:int = 2;
      
      public static const CLOSE_SCORE_WIN:int = 3;
      
      public static const CLOSE_LOG_SCORE_WIN:int = 4;
      
      public static const CLOSE_SORT_WIN:int = 5;
      
      public static const CLOSE_SAVE_WIN:int = 6;
      
      public static const CLOSE_REGSUCC_WIN:int = 7;
      
      public static const CLOSE_SHOP_WIN:int = 8;
      
      public static const CLOSE_SHOPITEM_WIN:int = 9;
      
      public static const ADDPRO_SUCCESS:String = "10000";
      
      public static const DELPRO_SUCCESS:String = "10000";
      
      public static const ADDFREEPRO_SUCCESS:String = "10000";
      
      public static const UPDATEPRO_SUCCESS:String = "10000";
      
      public static const NO_Enough_Money:String = "10001";
      
      public static const MODIFYEX_SUCCESS:String = "10000";
      
      public static const CLEARITEMS_SUCCESS:String = "10000";
      
      public static const Shop_Error_ShowUi:String = "90102|没有添加商城的Key|";
      
      public static const Package_Error_Delete:String = "90100|请求删除物品出错了|";
      
      public static const Package_Error_GetList:String = "90101|获取背包当前列表出错了|";
      
      public static const Package_Error_GetIdsList:String = "90103|根据物品id列表获取数据集出错了|";
      
      public static const Package_Error_UpdatePro:String = "90104|修改物品属性出错了|";
      
      public static const Package_Error_Add:String = "90105|请求添加免费物品出错了|";
      
      public static const Package_Error:String = "90106|操作背包数据出错了|";
      
      public static const Shop_Error_SetEx:String = "90107|设置扩展字段出错了|";
      
      public static const FreePackage_Error_GetList:String = "90108|获取免费物品数据集出错了|";
      
      public static const PayPackage_Error_GetList:String = "90109|获取收费物品数据集出错了|";
      
      public static const Shop_Error_TypeNotice:String = "90110|获取物品分类及公告失败|";
      
      public static const Shop_Error_Buy:String = "90111|购买物品失败|";
      
      public static const Shop_Error_ModifyEx:String = "90112|修改扩展字段出错了|";
      
      public static const Shop_Error_ClearItemsByExTypex:String = "90113|根据扩展字段及物品类型清空所有物品出错了|";
      
      public static const Shop_Update_Head:String = "0";
      
      public static const Shop_DelItems_Head:String = "1";
      
      public static const Shop_DelItem_Head:String = "2";
      
      public static const Shop_AddItems_Head:String = "3";
      
      public static const Shop_Buy_Head:String = "4";
      
      public static const Shop_ModifyEx_Head:String = "5";
      
      public static const Shop_ClearItems_Head:String = "6";
      
      public static const DataOK:String = "0";
      
      public static const TempStop:String = "1";
      
      public static const ForeverStop:String = "2";
      
      public static const TemExcepInfo:String = "该位置的存档数据存在异常，以临时封档处理。若有异议可进行申诉。";
      
      public static const ForeverExcepInfo:String = "该位置的存档数据频繁出现异常，以永久封档处理。请读取其他档数据。";
      
      public static const GetData_Excep:String = "getDataExcep";
      
      public static const SAVE_EVENT_DataExcep:int = 110;
      
      public static const AppealUrl:String = "https://save.api.4399.com/auth/r.php?app=feedback";
      
      public static const ReportUrl:String = "https://save.api.4399.com/auth/r.php?app=feedback-report";
      
      public static const GetSession:String = "https://save.api.4399.com/?ac=get_session";
      
      public static const GetStoreState:String = "https://save.api.4399.com/?ac=check_session";
      
      public static const MultipleError:String = "multiple_session";
      
      public static const MultipleErrorInfo:String = "抱歉！该游戏多开了,无法存读档操作！";
      
      public static const RL_ERROR_CLIENT_CODE:String = "99999";
      
      public static const RL_ERROR_CLIENT_MSG:String = "初始化网络客户端出错了";
      
      public static const RL_ERROR_ARG_CODE:String = "99998";
      
      public static const RL_ERROR_ARG_MSG:String = "传入的参数出错了";
      
      public static const RL_ERROR_CALLAPI_CODE:String = "99997";
      
      public static const RL_ERROR_CALLAPI_MSG:String = "接口调用出问题了";
      
      public static const RL_ERROR_USERDATA_CODE:String = "99996";
      
      public static const RL_ERROR_USERDATA_MSG:String = "读取用户数据出错了";
      
      public static const RL_ERROR_USERDATAEXCP_CODE:String = "99995";
      
      public static const RL_ERROR_USERDATAEXCP_MSG:String = "用户数据异常";
      
      public static const RL_ERROR_BACKDATA_CODE:String = "99994";
      
      public static const RL_ERROR_BACKDATA_MSG:String = "服务器端返回数据出错了";
      
      public static const RL_API_GRKI:String = "1";
      
      public static const RL_API_GRKByOwn:String = "2";
      
      public static const RL_API_SSTRK:String = "3";
      
      public static const RL_API_GRKD:String = "4";
      
      public static const RL_API_GUD:String = "5";
      
      public function AllConst()
      {
         super();
      }
   }
}

