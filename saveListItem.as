package
{
   import flash.display.Sprite;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol216")]
   public dynamic class saveListItem extends Sprite
   {
      
      public var disableSkin:mouseDisAbleSkin;
      
      public var downSkin:mouseDownSkin;
      
      public var indexTxt:TextField;
      
      public var outSkin:mouseOutSkin;
      
      public var overSkin:mouseOverSkin;
      
      public var selectSkin:selectSkin_sp;
      
      public var timeTxt:TextField;
      
      public var titleTxt:TextField;
      
      public function saveListItem()
      {
         super();
      }
   }
}

